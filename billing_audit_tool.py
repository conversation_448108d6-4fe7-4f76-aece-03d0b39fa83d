import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
from datetime import datetime
import os

warnings.filterwarnings('ignore')

class BillingAuditAnalyzer:
    def __init__(self, config_file='config.json'):
        """Initialize the billing audit analyzer with configuration."""
        self.config = self.load_config(config_file)
        self.data = None
        self.tariff_lookup = None
        self.results = {}
        
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def load_config(self, config_file):
        """Load configuration from JSON file."""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Config file {config_file} not found. Creating template...")
            self.create_config_template(config_file)
            print(f"Please edit {config_file} and run again.")
            exit(1)
    
    def create_config_template(self, config_file):
        """Create a configuration template."""
        template = {
            "data_file": "billing_data.csv",
            "tariff_file": "tariff_lookup.csv",
            "output_dir": "audit_results",
            "column_mapping": {
                "consumption_system": "consumption_system",
                "consumption_manual": "consumption_manual", 
                "bill_system": "bill_system",
                "bill_manual": "bill_manual",
                "tariff_code": "tariff_code",
                "customer_id": "customer_id",
                "date": "date"
            },
            "analysis_settings": {
                "top_n_customers": 20,
                "tolerance_threshold": 0.05,
                "export_formats": ["png", "pdf"]
            }
        }
        
        with open(config_file, 'w') as f:
            json.dump(template, f, indent=2)
    
    def load_data(self):
        """Load main billing data and tariff lookup."""
        print("Loading billing data...")
        
        # Load main data
        self.data = pd.read_csv(self.config['data_file'])
        print(f"Loaded {len(self.data)} billing records")
        
        # Load tariff lookup
        self.tariff_lookup = pd.read_csv(self.config['tariff_file'])
        self.tariff_lookup = self.tariff_lookup.dropna(subset=['tariff_code'])
        print(f"Loaded {len(self.tariff_lookup)} tariff codes")
        
        # Apply column mapping
        mapping = self.config['column_mapping']
        for key, col in mapping.items():
            if col in self.data.columns:
                self.data[key] = self.data[col]
        
        # Ensure output directory exists
        Path(self.config['output_dir']).mkdir(exist_ok=True)
        
    def classify_customers(self):
        """Classify customers as tiered or fixed based on tariff codes."""
        # Define tiered tariff codes (households)
        tiered_codes = ['5t5', 'Indigent KWH']
        
        # Merge with tariff lookup
        self.data = self.data.merge(
            self.tariff_lookup[['tariff_code', 'Cosumer tyoe']], 
            on='tariff_code', 
            how='left'
        )
        
        # Classify as tiered or fixed
        self.data['customer_type'] = self.data['tariff_code'].apply(
            lambda x: 'Tiered' if x in tiered_codes else 'Fixed'
        )
        
        # Calculate differences
        self.data['consumption_diff'] = (
            self.data['consumption_system'] - self.data['consumption_manual']
        )
        self.data['bill_diff'] = (
            self.data['bill_system'] - self.data['bill_manual']
        )
        self.data['bill_diff_abs'] = abs(self.data['bill_diff'])

        # Flag special cases for investigation
        self.data['zero_consumption_manual'] = (self.data['consumption_manual'] == 0)
        self.data['zero_consumption_system'] = (self.data['consumption_system'] == 0)
        self.data['negative_consumption_manual'] = (self.data['consumption_manual'] < 0)
        self.data['negative_consumption_system'] = (self.data['consumption_system'] < 0)
        self.data['special_case_flag'] = (
            self.data['zero_consumption_manual'] |
            self.data['zero_consumption_system'] |
            self.data['negative_consumption_manual'] |
            self.data['negative_consumption_system']
        )

        # Calculate mismatch flags with safe division
        tolerance = self.config['analysis_settings']['tolerance_threshold']

        # Safe consumption mismatch calculation
        self.data['consumption_mismatch'] = False
        valid_manual_consumption = (self.data['consumption_manual'] != 0) & (~self.data['special_case_flag'])
        self.data.loc[valid_manual_consumption, 'consumption_mismatch'] = (
            abs(self.data.loc[valid_manual_consumption, 'consumption_diff'] /
                self.data.loc[valid_manual_consumption, 'consumption_manual']) > tolerance
        )

        # Safe bill mismatch calculation
        self.data['bill_mismatch'] = False
        valid_manual_bill = (self.data['bill_manual'] != 0) & (~self.data['special_case_flag'])
        self.data.loc[valid_manual_bill, 'bill_mismatch'] = (
            abs(self.data.loc[valid_manual_bill, 'bill_diff'] /
                self.data.loc[valid_manual_bill, 'bill_manual']) > tolerance
        )

        # Flag all special cases as requiring investigation
        self.data.loc[self.data['special_case_flag'], 'consumption_mismatch'] = True
        self.data.loc[self.data['special_case_flag'], 'bill_mismatch'] = True
        
        print(f"Customer classification complete:")
        print(f"- Tiered customers: {sum(self.data['customer_type'] == 'Tiered')}")
        print(f"- Fixed customers: {sum(self.data['customer_type'] == 'Fixed')}")
        print(f"\nSpecial cases flagged for investigation:")
        print(f"- Zero consumption (manual): {sum(self.data['zero_consumption_manual'])}")
        print(f"- Zero consumption (system): {sum(self.data['zero_consumption_system'])}")
        print(f"- Negative consumption (manual): {sum(self.data['negative_consumption_manual'])}")
        print(f"- Negative consumption (system): {sum(self.data['negative_consumption_system'])}")
        print(f"- Total special cases: {sum(self.data['special_case_flag'])}")
    
    def create_scatter_plots(self):
        """Create scatter plots for system vs manual comparisons."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Consumption scatter
        ax1.scatter(self.data['consumption_manual'], self.data['consumption_system'], 
                   alpha=0.6, c=self.data['customer_type'].map({'Tiered': 'blue', 'Fixed': 'red'}))
        
        # Add 45° reference line
        max_consumption = max(self.data['consumption_manual'].max(), 
                            self.data['consumption_system'].max())
        ax1.plot([0, max_consumption], [0, max_consumption], 'k--', alpha=0.5, label='Perfect Match')
        
        ax1.set_xlabel('Manual Consumption (kWh)')
        ax1.set_ylabel('System Consumption (kWh)')
        ax1.set_title('System vs Manual Consumption')
        ax1.legend(['Perfect Match', 'Tiered', 'Fixed'])
        ax1.grid(True, alpha=0.3)
        
        # Bill scatter
        ax2.scatter(self.data['bill_manual'], self.data['bill_system'],
                   alpha=0.6, c=self.data['customer_type'].map({'Tiered': 'blue', 'Fixed': 'red'}))
        
        # Add 45° reference line
        max_bill = max(self.data['bill_manual'].max(), self.data['bill_system'].max())
        ax2.plot([0, max_bill], [0, max_bill], 'k--', alpha=0.5)
        
        ax2.set_xlabel('Manual Bill (R)')
        ax2.set_ylabel('System Bill (R)')
        ax2.set_title('System vs Manual Bills')
        ax2.legend(['Perfect Match', 'Tiered', 'Fixed'])
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.save_plot(fig, 'scatter_plots')
        plt.close()
    
    def create_distribution_plots(self):
        """Create distribution plots for differences."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Consumption difference histogram
        ax1.hist(self.data['consumption_diff'], bins=50, alpha=0.7, edgecolor='black')
        ax1.axvline(0, color='red', linestyle='--', alpha=0.7)
        ax1.set_xlabel('Consumption Difference (System - Manual)')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution of Consumption Differences')
        ax1.grid(True, alpha=0.3)
        
        # Bill difference histogram
        ax2.hist(self.data['bill_diff'], bins=50, alpha=0.7, edgecolor='black', color='orange')
        ax2.axvline(0, color='red', linestyle='--', alpha=0.7)
        ax2.set_xlabel('Bill Difference (System - Manual) (R)')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Distribution of Bill Differences')
        ax2.grid(True, alpha=0.3)
        
        # Density plots by customer type
        for ctype in ['Tiered', 'Fixed']:
            data_subset = self.data[self.data['customer_type'] == ctype]
            ax3.hist(data_subset['consumption_diff'], bins=30, alpha=0.6, 
                    label=f'{ctype} ({len(data_subset)} customers)', density=True)
        
        ax3.axvline(0, color='red', linestyle='--', alpha=0.7)
        ax3.set_xlabel('Consumption Difference (System - Manual)')
        ax3.set_ylabel('Density')
        ax3.set_title('Consumption Difference Distribution by Customer Type')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Bill differences by customer type
        for ctype in ['Tiered', 'Fixed']:
            data_subset = self.data[self.data['customer_type'] == ctype]
            ax4.hist(data_subset['bill_diff'], bins=30, alpha=0.6,
                    label=f'{ctype} ({len(data_subset)} customers)', density=True)
        
        ax4.axvline(0, color='red', linestyle='--', alpha=0.7)
        ax4.set_xlabel('Bill Difference (System - Manual) (R)')
        ax4.set_ylabel('Density')
        ax4.set_title('Bill Difference Distribution by Customer Type')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        self.save_plot(fig, 'distribution_plots')
        plt.close()
    
    def create_boxplots(self):
        """Create boxplots for comparative analysis."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Bill differences by customer type
        sns.boxplot(data=self.data, x='customer_type', y='bill_diff', ax=ax1)
        ax1.axhline(0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title('Bill Differences by Customer Type')
        ax1.set_ylabel('Bill Difference (R)')
        ax1.grid(True, alpha=0.3)
        
        # For tiered customers, analyze by consumption ranges
        tiered_data = self.data[self.data['customer_type'] == 'Tiered'].copy()
        if len(tiered_data) > 0:
            # Create consumption tiers
            tiered_data['consumption_tier'] = pd.cut(
                tiered_data['consumption_manual'],
                bins=[0, 50, 350, 600, float('inf')],
                labels=['1-50 kWh', '51-350 kWh', '351-600 kWh', '601+ kWh']
            )
            
            sns.boxplot(data=tiered_data, x='consumption_tier', y='bill_diff', ax=ax2)
            ax2.axhline(0, color='red', linestyle='--', alpha=0.7)
            ax2.set_title('Bill Differences by Consumption Tier (Tiered Customers)')
            ax2.set_ylabel('Bill Difference (R)')
            ax2.set_xlabel('Consumption Tier')
            ax2.tick_params(axis='x', rotation=45)
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, 'No tiered customers found', 
                    ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('Bill Differences by Consumption Tier')
        
        plt.tight_layout()
        self.save_plot(fig, 'boxplots')
        plt.close()
    
    def create_top_customers_chart(self):
        """Create bar chart of top N customers by absolute bill difference."""
        n = self.config['analysis_settings']['top_n_customers']
        
        top_customers = self.data.nlargest(n, 'bill_diff_abs')
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        colors = ['red' if x == 'Fixed' else 'blue' for x in top_customers['customer_type']]
        bars = ax.bar(range(len(top_customers)), top_customers['bill_diff_abs'], color=colors, alpha=0.7)
        
        ax.set_xlabel('Customer Rank')
        ax.set_ylabel('Absolute Bill Difference (R)')
        ax.set_title(f'Top {n} Customers by Absolute Bill Difference')
        ax.grid(True, alpha=0.3, axis='y')
        
        # Add value labels on bars
        for i, (bar, value) in enumerate(zip(bars, top_customers['bill_diff_abs'])):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.01,
                   f'R{value:.0f}', ha='center', va='bottom', fontsize=8)
        
        # Create legend
        from matplotlib.patches import Patch
        legend_elements = [Patch(facecolor='blue', alpha=0.7, label='Tiered'),
                          Patch(facecolor='red', alpha=0.7, label='Fixed')]
        ax.legend(handles=legend_elements)
        
        plt.xticks(range(0, len(top_customers), max(1, len(top_customers)//10)))
        plt.tight_layout()
        self.save_plot(fig, 'top_customers')
        plt.close()

        # Store for summary
        self.results['top_customers'] = top_customers

    def create_special_cases_analysis(self):
        """Create analysis chart for zero and negative consumption cases."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # Special cases summary - pie chart
        special_counts = [
            sum(self.data['zero_consumption_manual']),
            sum(self.data['zero_consumption_system']),
            sum(self.data['negative_consumption_manual']),
            sum(self.data['negative_consumption_system']),
            sum(~self.data['special_case_flag'])
        ]
        labels = ['Zero Manual', 'Zero System', 'Negative Manual', 'Negative System', 'Normal']
        colors = ['lightcoral', 'orange', 'red', 'darkred', 'lightgreen']

        # Only show non-zero categories
        non_zero_counts = [(count, label, color) for count, label, color in zip(special_counts, labels, colors) if count > 0]
        if non_zero_counts:
            counts, labels, colors = zip(*non_zero_counts)
            ax1.pie(counts, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('Special Cases Distribution')

        # Special cases by customer type
        special_by_type = self.data.groupby('customer_type')['special_case_flag'].agg(['sum', 'count'])
        special_by_type['rate'] = special_by_type['sum'] / special_by_type['count'] * 100

        bars = ax2.bar(special_by_type.index, special_by_type['rate'], color=['blue', 'red'], alpha=0.7)
        ax2.set_ylabel('Special Cases Rate (%)')
        ax2.set_title('Special Cases Rate by Customer Type')
        ax2.grid(True, alpha=0.3)

        # Add value labels
        for bar, rate in zip(bars, special_by_type['rate']):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{rate:.1f}%', ha='center', va='bottom')

        # Special cases details table
        ax3.axis('off')
        special_stats = [
            ['Zero Manual Consumption', f"{sum(self.data['zero_consumption_manual']):,}"],
            ['Zero System Consumption', f"{sum(self.data['zero_consumption_system']):,}"],
            ['Negative Manual Consumption', f"{sum(self.data['negative_consumption_manual']):,}"],
            ['Negative System Consumption', f"{sum(self.data['negative_consumption_system']):,}"],
            ['Total Special Cases', f"{sum(self.data['special_case_flag']):,}"],
            ['Special Cases Rate', f"{sum(self.data['special_case_flag'])/len(self.data)*100:.1f}%"]
        ]

        table = ax3.table(cellText=special_stats,
                         colLabels=['Special Case Type', 'Count'],
                         cellLoc='left',
                         loc='center',
                         colWidths=[0.7, 0.3])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)
        ax3.set_title('Special Cases Summary', pad=20)

        # Show top special cases customers
        special_cases_data = self.data[self.data['special_case_flag']].copy()
        if len(special_cases_data) > 0:
            # Get top 10 by absolute bill difference
            top_special = special_cases_data.nlargest(10, 'bill_diff_abs')

            ax4.barh(range(len(top_special)), top_special['bill_diff_abs'], alpha=0.7)
            ax4.set_yticks(range(len(top_special)))
            ax4.set_yticklabels([f"{row['customer_id']}\n({row['customer_type']})"
                               for _, row in top_special.iterrows()], fontsize=8)
            ax4.set_xlabel('Absolute Bill Difference (R)')
            ax4.set_title('Top Special Cases by Bill Impact')
            ax4.grid(True, alpha=0.3, axis='x')
        else:
            ax4.text(0.5, 0.5, 'No special cases found',
                    ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('Top Special Cases by Bill Impact')

        plt.tight_layout()
        self.save_plot(fig, 'special_cases_analysis')
        plt.close()

        # Store for summary
        self.results['special_stats'] = special_stats
        self.results['special_by_type'] = special_by_type
        if len(special_cases_data) > 0:
            self.results['top_special_cases'] = special_cases_data.nlargest(10, 'bill_diff_abs')
    
    def create_mismatch_summary(self):
        """Create mismatch rate summary visualizations."""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Overall mismatch rates - pie chart
        mismatch_counts = [
            sum(self.data['bill_mismatch']),
            sum(~self.data['bill_mismatch'])
        ]
        labels = ['Mismatched', 'Matched']
        colors = ['lightcoral', 'lightgreen']
        
        ax1.pie(mismatch_counts, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('Overall Bill Mismatch Rate')
        
        # Mismatch by customer type
        mismatch_by_type = self.data.groupby('customer_type')['bill_mismatch'].agg(['sum', 'count'])
        mismatch_by_type['rate'] = mismatch_by_type['sum'] / mismatch_by_type['count'] * 100
        
        bars = ax2.bar(mismatch_by_type.index, mismatch_by_type['rate'], color=['blue', 'red'], alpha=0.7)
        ax2.set_ylabel('Mismatch Rate (%)')
        ax2.set_title('Bill Mismatch Rate by Customer Type')
        ax2.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, rate in zip(bars, mismatch_by_type['rate']):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{rate:.1f}%', ha='center', va='bottom')
        
        # Consumption vs Bill mismatches
        mismatch_comparison = pd.DataFrame({
            'Consumption': [sum(self.data['consumption_mismatch']), sum(~self.data['consumption_mismatch'])],
            'Bill': [sum(self.data['bill_mismatch']), sum(~self.data['bill_mismatch'])]
        }, index=['Mismatched', 'Matched'])
        
        mismatch_comparison.plot(kind='bar', ax=ax3, color=['lightcoral', 'orange'], alpha=0.7)
        ax3.set_title('Consumption vs Bill Mismatches')
        ax3.set_ylabel('Number of Records')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # Summary statistics table
        ax4.axis('off')
        summary_stats = [
            ['Total Customers', f"{len(self.data):,}"],
            ['Tiered Customers', f"{sum(self.data['customer_type'] == 'Tiered'):,}"],
            ['Fixed Customers', f"{sum(self.data['customer_type'] == 'Fixed'):,}"],
            ['Bill Mismatches', f"{sum(self.data['bill_mismatch']):,}"],
            ['Mismatch Rate', f"{sum(self.data['bill_mismatch'])/len(self.data)*100:.1f}%"],
            ['Special Cases', f"{sum(self.data['special_case_flag']):,}"],
            ['Special Cases Rate', f"{sum(self.data['special_case_flag'])/len(self.data)*100:.1f}%"],
            ['Total Bill Variance', f"R {abs(self.data['bill_diff']).sum():,.0f}"],
            ['Mean Bill Difference', f"R {self.data['bill_diff'].mean():.2f}"],
            ['Median Bill Difference', f"R {self.data['bill_diff'].median():.2f}"]
        ]
        
        table = ax4.table(cellText=summary_stats, 
                         colLabels=['Metric', 'Value'],
                         cellLoc='left',
                         loc='center',
                         colWidths=[0.6, 0.4])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)
        ax4.set_title('Summary Statistics', pad=20)
        
        plt.tight_layout()
        self.save_plot(fig, 'mismatch_summary')
        plt.close()
        
        # Store for summary report
        self.results['summary_stats'] = summary_stats
        self.results['mismatch_by_type'] = mismatch_by_type
    
    def create_time_series(self):
        """Create time series analysis if date column exists."""
        if 'date' not in self.data.columns or self.data['date'].isna().all():
            print("No date column found or all dates are null. Skipping time series analysis.")
            return
        
        try:
            # Convert date column
            self.data['date'] = pd.to_datetime(self.data['date'])
            
            # Monthly aggregation
            monthly_data = self.data.groupby([self.data['date'].dt.to_period('M'), 'customer_type']).agg({
                'consumption_system': 'sum',
                'consumption_manual': 'sum',
                'bill_system': 'sum',
                'bill_manual': 'sum',
                'bill_diff': 'sum'
            }).reset_index()
            
            if len(monthly_data) == 0:
                print("No monthly data available for time series.")
                return
            
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            
            # Plot by customer type
            for ctype in monthly_data['customer_type'].unique():
                data_subset = monthly_data[monthly_data['customer_type'] == ctype]
                ax1.plot(data_subset['date'].astype(str), data_subset['consumption_system'], 
                        marker='o', label=f'{ctype} - System')
                ax1.plot(data_subset['date'].astype(str), data_subset['consumption_manual'],
                        marker='s', label=f'{ctype} - Manual', linestyle='--')
            
            ax1.set_title('Monthly Consumption Trends')
            ax1.set_ylabel('Total Consumption (kWh)')
            ax1.legend()
            ax1.tick_params(axis='x', rotation=45)
            ax1.grid(True, alpha=0.3)
            
            # Bill trends
            for ctype in monthly_data['customer_type'].unique():
                data_subset = monthly_data[monthly_data['customer_type'] == ctype]
                ax2.plot(data_subset['date'].astype(str), data_subset['bill_system'],
                        marker='o', label=f'{ctype} - System')
                ax2.plot(data_subset['date'].astype(str), data_subset['bill_manual'],
                        marker='s', label=f'{ctype} - Manual', linestyle='--')
            
            ax2.set_title('Monthly Bill Trends')
            ax2.set_ylabel('Total Bills (R)')
            ax2.legend()
            ax2.tick_params(axis='x', rotation=45)
            ax2.grid(True, alpha=0.3)
            
            # Variance trends
            for ctype in monthly_data['customer_type'].unique():
                data_subset = monthly_data[monthly_data['customer_type'] == ctype]
                ax3.plot(data_subset['date'].astype(str), data_subset['bill_diff'],
                        marker='o', label=f'{ctype}')
            
            ax3.axhline(0, color='red', linestyle='--', alpha=0.7)
            ax3.set_title('Monthly Bill Variance Trends')
            ax3.set_ylabel('Total Bill Difference (R)')
            ax3.legend()
            ax3.tick_params(axis='x', rotation=45)
            ax3.grid(True, alpha=0.3)
            
            # Overall variance
            overall_monthly = self.data.groupby(self.data['date'].dt.to_period('M')).agg({
                'bill_diff_abs': 'sum',
                'bill_mismatch': 'sum'
            }).reset_index()
            
            ax4.bar(overall_monthly['date'].astype(str), overall_monthly['bill_diff_abs'], alpha=0.7)
            ax4_twin = ax4.twinx()
            ax4_twin.plot(overall_monthly['date'].astype(str), overall_monthly['bill_mismatch'], 
                         color='red', marker='o')
            
            ax4.set_title('Monthly Total Variance & Mismatches')
            ax4.set_ylabel('Total Absolute Difference (R)')
            ax4_twin.set_ylabel('Number of Mismatches', color='red')
            ax4.tick_params(axis='x', rotation=45)
            ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            self.save_plot(fig, 'time_series')
            plt.close()
            
        except Exception as e:
            print(f"Error creating time series: {e}")
    
    def save_plot(self, fig, name):
        """Save plot in specified formats."""
        formats = self.config['analysis_settings']['export_formats']
        
        for fmt in formats:
            output_path = Path(self.config['output_dir']) / f"{name}.{fmt}"
            fig.savefig(output_path, dpi=300, bbox_inches='tight')
        
        print(f"Saved {name} plot")
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report."""
        output_path = Path(self.config['output_dir']) / 'audit_summary_report.txt'
        
        with open(output_path, 'w') as f:
            f.write("BILLING AUDIT ANALYSIS SUMMARY REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Summary statistics
            f.write("SUMMARY STATISTICS\n")
            f.write("-" * 20 + "\n")
            for metric, value in self.results['summary_stats']:
                f.write(f"{metric:.<30} {value}\n")
            f.write("\n")
            
            # Special cases analysis
            f.write("SPECIAL CASES ANALYSIS\n")
            f.write("-" * 25 + "\n")
            for metric, value in self.results['special_stats']:
                f.write(f"{metric:.<35} {value}\n")
            f.write("\n")

            # Mismatch analysis
            f.write("MISMATCH ANALYSIS BY CUSTOMER TYPE\n")
            f.write("-" * 35 + "\n")
            for ctype, data in self.results['mismatch_by_type'].iterrows():
                f.write(f"{ctype}:\n")
                f.write(f"  Total Customers: {data['count']}\n")
                f.write(f"  Mismatches: {data['sum']}\n")
                f.write(f"  Mismatch Rate: {data['rate']:.1f}%\n\n")
            
            # Top problematic customers
            f.write("TOP 10 CUSTOMERS BY BILL DIFFERENCE\n")
            f.write("-" * 35 + "\n")
            top_10 = self.results['top_customers'].head(10)
            for i, (_, customer) in enumerate(top_10.iterrows(), 1):
                f.write(f"{i:2d}. Customer ID: {customer['customer_id']} "
                       f"({customer['customer_type']}) - R{customer['bill_diff_abs']:.2f}\n")

            f.write("\n")

            # Top special cases for investigation
            if 'top_special_cases' in self.results:
                f.write("TOP 10 SPECIAL CASES FOR INVESTIGATION\n")
                f.write("-" * 40 + "\n")
                for i, (_, customer) in enumerate(self.results['top_special_cases'].iterrows(), 1):
                    flags = []
                    if customer['zero_consumption_manual']: flags.append('Zero Manual')
                    if customer['zero_consumption_system']: flags.append('Zero System')
                    if customer['negative_consumption_manual']: flags.append('Negative Manual')
                    if customer['negative_consumption_system']: flags.append('Negative System')

                    f.write(f"{i:2d}. Customer ID: {customer['customer_id']} "
                           f"({customer['customer_type']}) - R{customer['bill_diff_abs']:.2f}\n")
                    f.write(f"    Issues: {', '.join(flags)}\n")
                    f.write(f"    Manual: {customer['consumption_manual']:.2f} kWh, "
                           f"System: {customer['consumption_system']:.2f} kWh\n\n")

            f.write("\n")
            
            # Recommendations
            f.write("AUDIT RECOMMENDATIONS\n")
            f.write("-" * 20 + "\n")
            f.write("1. PRIORITY: Investigate all special cases (zero/negative consumption)\n")
            f.write("2. Focus audit efforts on the top 20 customers listed above\n")
            f.write("3. Investigate systematic issues in Fixed customer billing\n")
            f.write("4. Review meter reading processes for zero consumption cases\n")
            f.write("5. Check for solar feed-in or meter reversal issues (negative consumption)\n")
            f.write("6. Implement additional validation checks for high-variance accounts\n")
            f.write("7. Consider separate audit procedures for tiered vs fixed customers\n\n")

            # Files generated
            f.write("FILES GENERATED\n")
            f.write("-" * 15 + "\n")
            f.write("- scatter_plots.png: System vs Manual comparisons\n")
            f.write("- distribution_plots.png: Variance distributions\n")
            f.write("- boxplots.png: Customer type and tier comparisons\n")
            f.write("- top_customers.png: Highest variance customers\n")
            f.write("- mismatch_summary.png: Overall mismatch analysis\n")
            f.write("- special_cases_analysis.png: Zero/negative consumption analysis\n")
            f.write("- time_series.png: Trend analysis (if dates available)\n")
            f.write("- audit_summary_report.txt: This comprehensive report\n")
        
        print(f"Summary report generated: {output_path}")

    def export_special_cases(self):
        """Export special cases to CSV for detailed investigation."""
        special_cases = self.data[self.data['special_case_flag']].copy()

        if len(special_cases) > 0:
            # Add flag descriptions
            special_cases['investigation_flags'] = special_cases.apply(
                lambda row: ', '.join([
                    flag for flag, condition in [
                        ('Zero Manual Consumption', row['zero_consumption_manual']),
                        ('Zero System Consumption', row['zero_consumption_system']),
                        ('Negative Manual Consumption', row['negative_consumption_manual']),
                        ('Negative System Consumption', row['negative_consumption_system'])
                    ] if condition
                ]), axis=1
            )

            # Select relevant columns for investigation (using original column names)
            original_columns = [
                'Meter_Number', 'Rate_Tariff', 'customer_type', 'Priod_Date',
                'CONSUMPTION', 'iUnits', 'consumption_diff',
                'Total', 'ExclusiveAmount', 'bill_diff', 'bill_diff_abs',
                'investigation_flags'
            ]

            # Check which columns exist in the data
            investigation_columns = [col for col in original_columns if col in special_cases.columns]

            output_path = Path(self.config['output_dir']) / 'special_cases_for_investigation.csv'
            special_cases[investigation_columns].to_csv(output_path, index=False)

            print(f"Special cases exported for investigation: {output_path}")
            print(f"Found {len(special_cases)} special cases requiring investigation")
        else:
            print("No special cases found - no investigation file needed")
    
    def run_full_analysis(self):
        """Run the complete billing audit analysis."""
        print("Starting Billing Audit Analysis...")
        print("=" * 40)
        
        try:
            # Load and prepare data
            self.load_data()
            self.classify_customers()
            
            # Generate all visualizations
            print("\nGenerating visualizations...")
            self.create_scatter_plots()
            self.create_distribution_plots()
            self.create_boxplots()
            self.create_top_customers_chart()
            self.create_mismatch_summary()
            self.create_special_cases_analysis()
            self.create_time_series()
            
            # Generate summary report and export special cases
            self.generate_summary_report()
            self.export_special_cases()
            
            print("\n" + "=" * 40)
            print("ANALYSIS COMPLETE!")
            print(f"Results saved to: {self.config['output_dir']}")
            print("\nFiles generated:")
            print("- All visualization charts (PNG/PDF)")
            print("- Comprehensive summary report")
            print("- Special cases CSV for investigation")
            print("\nReady for audit presentation!")
            
        except Exception as e:
            print(f"Error during analysis: {e}")
            raise

# Main execution
if __name__ == "__main__":
    # Initialize and run analyzer
    analyzer = BillingAuditAnalyzer()
    analyzer.run_full_analysis()