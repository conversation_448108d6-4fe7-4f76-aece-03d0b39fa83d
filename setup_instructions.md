# Billing Audit Visualization Tool - Setup & Usage Guide

## Overview
This tool generates comprehensive audit visualizations from billing data, comparing system vs manual readings. It's designed specifically for electricity billing audits with support for both tiered (household) and fixed (business) customer types.

## Files Included
1. `billing_audit_analyzer.py` - Main analysis script
2. `config.json` - Configuration template
3. `sample_data_generator.py` - Test data generator
4. `tariff_lookup.csv` - Your existing tariff classification file

## Setup Instructions

### 1. Install Required Dependencies
```bash
pip install pandas numpy matplotlib seaborn pathlib
```

### 2. Prepare Your Data Files

#### Main Billing Data File
Your CSV should contain columns for:
- Meter_Number (Customer/Meter ID)
- iUnits (System consumption readings)
- CONSUMPTION (Manual consumption readings)
- ExclusiveAmount (System generated bills)
- Total (Expected/manual bills)
- Rate_Tariff (Tariff codes)
- Priod_Date (Billing period date)

#### Tariff Lookup File
Use your existing `tariff_lookup.csv` with:
- `tariff_code`: The tariff codes (5t5, 7t8, etc.)
- `Cosumer tyoe`: Consumer type description
- `tariff_rate`: Rate values (optional for analysis)

### 3. Configure Column Mappings

Edit `config.json` to match your data structure:

```json
{
  "data_file": "your_billing_data.csv",
  "tariff_file": "tariff_lookup.csv",
  "output_dir": "audit_results",
  "column_mapping": {
    "consumption_system": "iUnits",
    "consumption_manual": "CONSUMPTION",
    "bill_system": "ExclusiveAmount",
    "bill_manual": "Total",
    "tariff_code": "Rate_Tariff",
    "customer_id": "Meter_Number",
    "date": "Priod_Date"
  },
  "analysis_settings": {
    "top_n_customers": 20,
    "tolerance_threshold": 0.05,
    "export_formats": ["png", "pdf"]
  }
}
```

## Usage

### Testing with Sample Data
```bash
# Generate test data first
python sample_data_generator.py

# Run analysis on sample data  
python billing_audit_analyzer.py
```

### Running with Your Actual Data
1. Update `config.json` with your file names and column mappings
2. Ensure your data files are in the same directory
3. Run the analysis:
```bash
python billing_audit_analyzer.py
```

## Output Files

The tool generates:

### Visualizations (PNG/PDF):
- **scatter_plots**: System vs Manual comparisons with 45° reference lines
- **distribution_plots**: Histograms of consumption and bill differences  
- **boxplots**: Comparative analysis by customer type and consumption tiers
- **top_customers**: Bar chart of highest variance customers
- **mismatch_summary**: Overall mismatch rates and statistics
- **time_series**: Monthly trend analysis (if dates available)

### Reports:
- **audit_summary_report.txt**: Comprehensive text summary with key findings

## Understanding Customer Classification

Based on your tariff structure:

### Tiered Customers (Households):
- **Tariff Codes**: 5t5, Indigent KWH
- **Billing**: Progressive tiers (1-50, 51-350, 351-600, 601+ kWh)
- **Special**: Indigent customers get first 50 kWh free

### Fixed Customers (Businesses):
- **Tariff Codes**: 7t8, 7t5, 6t7, 6t9, 6t5, 7t6  
- **Billing**: Fixed rate per kWh
- **Types**: Business, Streetlights, Transnet, Departmental, etc.

## Key Visualizations Explained

### 1. Scatter Plots
- Points on 45° line = perfect agreement
- Points above line = system reads higher than manual
- Points below line = system reads lower than manual
- Colors distinguish tiered vs fixed customers

### 2. Distribution Plots  
- Show whether errors are normally distributed
- Identify systematic bias (shifted from zero)
- Compare error patterns between customer types

### 3. Boxplots
- Compare variance between customer types
- Analyze household tiers separately
- Identify which groups have highest variance

### 4. Top Customers Chart
- Pareto analysis: focus audit on highest impact customers
- Color coded by customer type
- Shows absolute bill differences

### 5. Mismatch Summary
- Overall audit readiness KPIs
- Breakdown by customer type
- Comparison of consumption vs bill mismatches

## Audit Recommendations

The tool automatically generates recommendations including:
- Priority customers for detailed audit
- Systematic issues to investigate  
- Process improvements needed
- Risk assessment by customer type

## Troubleshooting

### Common Issues:

1. **"Config file not found"**
   - Run the script once to generate template
   - Edit the generated `config.json`

2. **"Column not found"**  
   - Check column names in your CSV
   - Update `column_mapping` in config.json

3. **"No tiered customers found"**
   - Verify tariff codes in your data match `tariff_lookup.csv`
   - Check that 5t5 and Indigent KWH codes exist

4. **"Memory error with large files"**
   - Process in chunks or filter data first
   - Reduce number of visualizations if needed

### Data Quality Checks:
- Ensure no negative consumption values
- Check for reasonable bill amounts
- Verify tariff codes are valid
- Remove duplicate customer records

## Customization Options

### Modify Analysis Settings:
- `top_n_customers`: Number of top variance customers to highlight
- `tolerance_threshold`: Mismatch threshold (5% default)
- `export_formats`: Output formats ["png", "pdf"]

### Add Custom Visualizations:
The script is modular - you can easily add new visualization functions following the existing patterns.

## Presentation Ready Output

All charts are high-resolution (300 DPI) and professionally formatted for:
- Executive presentations
- Audit reports
- Regulatory submissions
- Internal reviews

The summary report provides talking points and key findings ready for management presentation.

---

**Need Help?** 
- Check that all required files exist
- Verify column mappings match your data
- Ensure tariff codes are consistent between files
- Test with sample data first before using actual data