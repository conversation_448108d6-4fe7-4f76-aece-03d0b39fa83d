import pandas as pd
import numpy as np

def analyze_audit_results():
    """Analyze the audit results for inconsistencies and issues."""
    
    print("=== AUDIT RESULTS ANALYSIS ===\n")
    
    # Load original sample data
    df = pd.read_csv('sample_billing_data_new.csv')
    print("ORIGINAL DATA:")
    print(f"- Total records: {len(df)}")
    print(f"- Unique customers: {df['Meter_Number'].nunique()}")
    print(f"- Duplicate rows: {df.duplicated().sum()}")
    print()
    
    # Check special cases in original data
    zero_manual = (df['CONSUMPTION'] == 0).sum()
    zero_system = (df['iUnits'] == 0).sum()
    neg_manual = (df['CONSUMPTION'] < 0).sum()
    neg_system = (df['iUnits'] < 0).sum()
    
    print("SPECIAL CASES IN ORIGINAL DATA:")
    print(f"- Zero manual consumption: {zero_manual}")
    print(f"- Zero system consumption: {zero_system}")
    print(f"- Negative manual consumption: {neg_manual}")
    print(f"- Negative system consumption: {neg_system}")
    print()
    
    # Check negative consumption billing rule
    neg_consumption_data = df[df['CONSUMPTION'] < 0]
    print("NEGATIVE CONSUMPTION BILLING RULE:")
    print(f"- Negative consumption cases: {len(neg_consumption_data)}")
    print(f"- All have Total = 0: {all(neg_consumption_data['Total'] == 0.0)}")
    print()
    
    # Load special cases file
    try:
        special_df = pd.read_csv('audit_results/special_cases_for_investigation.csv')
        print("SPECIAL CASES FILE:")
        print(f"- Total records: {len(special_df)}")
        print(f"- Unique customers: {special_df['Meter_Number'].nunique()}")
        print(f"- Duplicate rows: {special_df.duplicated().sum()}")
        
        # Check for duplicate customers
        duplicate_customers = special_df['Meter_Number'].value_counts()
        duplicates = duplicate_customers[duplicate_customers > 1]
        if len(duplicates) > 0:
            print(f"- Customers with multiple entries: {len(duplicates)}")
            print("  Top duplicates:")
            for customer, count in duplicates.head(5).items():
                print(f"    {customer}: {count} entries")
        print()
        
    except FileNotFoundError:
        print("Special cases file not found!")
        print()
    
    # Analyze bill differences
    df['bill_diff'] = df['ExclusiveAmount'] - df['Total']
    df['bill_diff_abs'] = abs(df['bill_diff'])
    
    print("BILL DIFFERENCE ANALYSIS:")
    print(f"- Max absolute difference: R{df['bill_diff_abs'].max():,.2f}")
    print(f"- Mean absolute difference: R{df['bill_diff_abs'].mean():.2f}")
    print(f"- Median absolute difference: R{df['bill_diff_abs'].median():.2f}")
    print(f"- 95th percentile: R{df['bill_diff_abs'].quantile(0.95):.2f}")
    print()
    
    # Check for unrealistic values
    print("POTENTIAL ISSUES:")
    
    # Very high bill differences
    high_diffs = df[df['bill_diff_abs'] > 50000]
    if len(high_diffs) > 0:
        print(f"- {len(high_diffs)} customers with bill differences > R50,000")
        print("  Examples:")
        for _, row in high_diffs.head(3).iterrows():
            print(f"    {row['Meter_Number']}: Consumption={row['CONSUMPTION']:.1f}, "
                  f"Total=R{row['Total']:.2f}, Exclusive=R{row['ExclusiveAmount']:.2f}")
    
    # Check for impossible scenarios
    zero_consumption_with_bills = df[(df['CONSUMPTION'] == 0) & (df['iUnits'] == 0) & 
                                   ((df['Total'] != 0) | (df['ExclusiveAmount'] != 0))]
    if len(zero_consumption_with_bills) > 0:
        print(f"- {len(zero_consumption_with_bills)} cases with zero consumption but non-zero bills")
    
    # Check negative consumption with non-zero Total
    neg_with_bills = df[(df['CONSUMPTION'] < 0) & (df['Total'] != 0)]
    if len(neg_with_bills) > 0:
        print(f"- ERROR: {len(neg_with_bills)} negative consumption cases with non-zero Total!")
        print("  This violates the business rule!")
    
    # Investigate specific problem customers
    print("\nPROBLEM CUSTOMER DETAILS:")
    problem_customers = ['MTR000151', 'MTR000429', 'MTR000432']
    for customer in problem_customers:
        customer_data = df[df['Meter_Number'] == customer]
        if len(customer_data) > 0:
            row = customer_data.iloc[0]
            print(f"{customer}:")
            print(f"  Rate_Tariff: {row['Rate_Tariff']}")
            print(f"  CONSUMPTION: {row['CONSUMPTION']}")
            print(f"  iUnits: {row['iUnits']}")
            print(f"  Total: R{row['Total']:,.2f}")
            print(f"  ExclusiveAmount: R{row['ExclusiveAmount']:,.2f}")
            print(f"  Difference: R{abs(row['ExclusiveAmount'] - row['Total']):,.2f}")
            print()

    print("\n=== ANALYSIS COMPLETE ===")

if __name__ == "__main__":
    analyze_audit_results()
