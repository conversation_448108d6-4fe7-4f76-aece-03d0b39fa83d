import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def generate_sample_billing_data(num_records=1000, output_file='sample_billing_data.csv'):
    """Generate sample billing data for testing the audit tool."""
    
    np.random.seed(42)  # For reproducible results
    
    # Define tariff codes based on your tariff lookup
    tiered_codes = ['5t5', 'Indigent KWH']
    fixed_codes = ['7t8', '7t5', '6t7', '6t9', '6t5', '7t6']
    
    all_tariff_codes = tiered_codes + fixed_codes
    
    data = []
    
    for i in range(num_records):
        customer_id = f"CUST{i+1:06d}"
        tariff_code = random.choice(all_tariff_codes)
        
        # Generate base consumption (manual reading)
        if tariff_code in tiered_codes:
            # Household consumption pattern
            base_consumption = np.random.lognormal(mean=5.5, sigma=0.8)  # ~200-300 kWh average
            base_consumption = max(10, min(base_consumption, 2000))  # Reasonable bounds
        else:
            # Business consumption pattern (higher)
            base_consumption = np.random.lognormal(mean=7.0, sigma=1.0)  # ~1000+ kWh average
            base_consumption = max(100, min(base_consumption, 10000))  # Reasonable bounds
        
        consumption_manual = round(base_consumption, 2)
        
        # Generate system consumption with some error
        error_factor = np.random.normal(1.0, 0.1)  # Usually close to 1, but can vary
        
        # Add some systematic bias for demonstration
        if random.random() < 0.1:  # 10% chance of large error
            error_factor = np.random.choice([0.5, 1.5, 2.0])  # Significant under/over reading
        
        consumption_system = round(consumption_manual * error_factor, 2)
        consumption_system = max(0, consumption_system)  # Can't be negative
        
        # Calculate bills based on tariff structure
        if tariff_code == '5t5':  # Regular household
            bill_manual = calculate_tiered_bill(consumption_manual, 'regular')
            bill_system = calculate_tiered_bill(consumption_system, 'regular')
        elif tariff_code == 'Indigent KWH':  # Indigent household
            bill_manual = calculate_tiered_bill(consumption_manual, 'indigent')
            bill_system = calculate_tiered_bill(consumption_system, 'indigent')
        else:  # Fixed rate customers
            # Use rates from tariff lookup (simplified)
            rate_map = {'7t8': 5, '7t5': 2, '6t7': 7, '6t9': 12, '6t5': 45, '7t6': 19}
            rate = rate_map.get(tariff_code, 5)
            
            bill_manual = round(consumption_manual * rate, 2)
            bill_system = round(consumption_system * rate, 2)
        
        # Generate random date within last 12 months
        start_date = datetime.now() - timedelta(days=365)
        random_date = start_date + timedelta(days=random.randint(0, 365))
        
        data.append({
            'customer_id': customer_id,
            'consumption_manual': consumption_manual,
            'consumption_system': consumption_system,
            'bill_manual': bill_manual,
            'bill_system': bill_system,
            'tariff_code': tariff_code,
            'date': random_date.strftime('%Y-%m-%d')
        })
    
    # Create DataFrame and save
    df = pd.DataFrame(data)
    df.to_csv(output_file, index=False)
    
    print(f"Generated {num_records} sample billing records")
    print(f"Saved to: {output_file}")
    print(f"\nSample statistics:")
    print(f"- Tiered customers: {sum(df['tariff_code'].isin(tiered_codes))}")
    print(f"- Fixed customers: {sum(df['tariff_code'].isin(fixed_codes))}")
    print(f"- Average consumption (manual): {df['consumption_manual'].mean():.1f} kWh")
    print(f"- Average bill (manual): R{df['bill_manual'].mean():.2f}")
    
    return df

def calculate_tiered_bill(consumption, customer_type='regular'):
    """Calculate bill using tiered pricing structure."""
    
    if customer_type == 'indigent':
        # First 50 kWh free, then regular rates
        if consumption <= 50:
            return 0.0
        else:
            remaining = consumption - 50
            return calculate_tiered_bill(remaining, 'regular')
    
    # Regular household tiered rates
    bill = 0.0
    remaining = consumption
    
    # Tier 1: 1-50 kWh at R1.5
    if remaining > 0:
        tier1 = min(remaining, 50)
        bill += tier1 * 1.5
        remaining -= tier1
    
    # Tier 2: 51-350 kWh at R2.0
    if remaining > 0:
        tier2 = min(remaining, 300)  # 350-50 = 300
        bill += tier2 * 2.0
        remaining -= tier2
    
    # Tier 3: 351-600 kWh at R2.5
    if remaining > 0:
        tier3 = min(remaining, 250)  # 600-350 = 250
        bill += tier3 * 2.5
        remaining -= tier3
    
    # Tier 4: 601+ kWh at R3.0
    if remaining > 0:
        bill += remaining * 3.0
    
    return round(bill, 2)

if __name__ == "__main__":
    # Generate sample data for testing
    generate_sample_billing_data(1000, 'sample_billing_data.csv')
    
    print("\nSample data generated successfully!")
    print("You can now test the billing audit tool with this data.")
    print("\nTo use with your actual data:")
    print("1. Replace 'sample_billing_data.csv' with your actual file in config.json")
    print("2. Update column mappings in config.json to match your column names")
    print("3. Ensure your tariff_lookup.csv file is in the correct format")
    print("4. Run the main analysis script")