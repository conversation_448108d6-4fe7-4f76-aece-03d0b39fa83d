import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def generate_sample_billing_data(num_records=1000, output_file='sample_billing_data.csv'):
    """Generate sample billing data for testing the audit tool."""
    
    np.random.seed(42)  # For reproducible results
    
    # Define tariff codes based on your tariff lookup
    tiered_codes = ['5t5', 'Indigent KWH']
    fixed_codes = ['7t8', '7t5', '6t7', '6t9', '6t5', '7t6']
    
    all_tariff_codes = tiered_codes + fixed_codes
    
    data = []
    
    for i in range(num_records):
        meter_number = f"MTR{i+1:06d}"
        rate_tariff = random.choice(all_tariff_codes)

        # Determine if this should be a special case (zero/negative consumption)
        special_case = random.random()

        if special_case < 0.03:  # 3% chance of zero consumption
            consumption_manual = 0.0
            consumption_system = 0.0 if random.random() < 0.7 else round(np.random.uniform(0, 50), 2)
        elif special_case < 0.05:  # 2% chance of negative consumption (solar feed-in, meter reversal)
            consumption_manual = round(np.random.uniform(-500, -10), 2)
            # System might read differently for negative values
            if random.random() < 0.3:  # 30% chance system reads zero when manual is negative
                consumption_system = 0.0
            else:
                error_factor = np.random.normal(1.0, 0.2)
                consumption_system = round(consumption_manual * error_factor, 2)
        else:
            # Normal consumption generation
            if rate_tariff in tiered_codes:
                # Household consumption pattern
                base_consumption = np.random.lognormal(mean=5.5, sigma=0.8)  # ~200-300 kWh average
                base_consumption = max(10, min(base_consumption, 1500))  # Reasonable bounds for households
            else:
                # Business consumption pattern (higher but realistic)
                base_consumption = np.random.lognormal(mean=6.5, sigma=0.8)  # ~600-800 kWh average
                base_consumption = max(100, min(base_consumption, 3000))  # Reasonable bounds for business

            consumption_manual = round(base_consumption, 2)

            # Generate system consumption with some error
            error_factor = np.random.normal(1.0, 0.1)  # Usually close to 1, but can vary

            # Add some systematic bias for demonstration
            if random.random() < 0.1:  # 10% chance of large error
                error_factor = np.random.choice([0.5, 1.5, 2.0])  # Significant under/over reading

            consumption_system = round(consumption_manual * error_factor, 2)
        
        # Calculate bills based on tariff structure
        if rate_tariff == '5t5':  # Regular household
            bill_manual = calculate_tiered_bill(consumption_manual, 'regular')
            bill_system = calculate_tiered_bill(consumption_system, 'regular')
        elif rate_tariff == 'Indigent KWH':  # Indigent household
            bill_manual = calculate_tiered_bill(consumption_manual, 'indigent')
            bill_system = calculate_tiered_bill(consumption_system, 'indigent')
        else:  # Fixed rate customers
            # Use rates from tariff lookup (simplified)
            rate_map = {'7t8': 5, '7t5': 2, '6t7': 7, '6t9': 12, '6t5': 45, '7t6': 19}
            rate = rate_map.get(rate_tariff, 5)

            bill_manual = round(consumption_manual * rate, 2)
            bill_system = round(consumption_system * rate, 2)

        # Handle negative consumption billing rule: Total should be zero for negative consumption
        if consumption_manual < 0:
            bill_manual = 0.0
        
        # Generate random date within last 12 months
        start_date = datetime.now() - timedelta(days=365)
        random_date = start_date + timedelta(days=random.randint(0, 365))

        # Generate additional fields to match your actual data structure
        account_number = f"ACC{i+1:06d}"
        erf_number = f"ERF{random.randint(1000, 9999)}"
        meter_id = f"MID{i+1:06d}"

        data.append({
            'Priod_Date': random_date.strftime('%Y-%m-%d'),
            'Account': account_number,
            'Meter_Number': meter_number,
            'ERF_Number': erf_number,
            'MeterID': meter_id,
            'Rate_Tariff': rate_tariff,
            'CONSUMPTION': consumption_manual,
            'iUnits': consumption_system,
            'ExclusiveAmount': bill_system,
            'Total': bill_manual,
            'CONS MATCH': 'Y' if abs(consumption_manual - consumption_system) < 1 else 'N',
            'BILL MATCH': 'Y' if abs(bill_manual - bill_system) < 1 else 'N'
        })
    
    # Create DataFrame and save
    df = pd.DataFrame(data)
    df.to_csv(output_file, index=False)
    
    print(f"Generated {num_records} sample billing records")
    print(f"Saved to: {output_file}")
    print(f"\nSample statistics:")
    print(f"- Tiered customers: {sum(df['Rate_Tariff'].isin(tiered_codes))}")
    print(f"- Fixed customers: {sum(df['Rate_Tariff'].isin(fixed_codes))}")
    print(f"- Average consumption (manual): {df['CONSUMPTION'].mean():.1f} kWh")
    print(f"- Average bill (manual): R{df['Total'].mean():.2f}")
    print(f"- Zero consumption cases: {sum(df['CONSUMPTION'] == 0)}")
    print(f"- Negative consumption cases: {sum(df['CONSUMPTION'] < 0)}")
    
    return df

def calculate_tiered_bill(consumption, customer_type='regular'):
    """Calculate bill using tiered pricing structure."""
    
    if customer_type == 'indigent':
        # First 50 kWh free, then regular rates
        if consumption <= 50:
            return 0.0
        else:
            remaining = consumption - 50
            return calculate_tiered_bill(remaining, 'regular')
    
    # Regular household tiered rates
    bill = 0.0
    remaining = consumption
    
    # Tier 1: 1-50 kWh at R1.5
    if remaining > 0:
        tier1 = min(remaining, 50)
        bill += tier1 * 1.5
        remaining -= tier1
    
    # Tier 2: 51-350 kWh at R2.0
    if remaining > 0:
        tier2 = min(remaining, 300)  # 350-50 = 300
        bill += tier2 * 2.0
        remaining -= tier2
    
    # Tier 3: 351-600 kWh at R2.5
    if remaining > 0:
        tier3 = min(remaining, 250)  # 600-350 = 250
        bill += tier3 * 2.5
        remaining -= tier3
    
    # Tier 4: 601+ kWh at R3.0
    if remaining > 0:
        bill += remaining * 3.0
    
    return round(bill, 2)

if __name__ == "__main__":
    # Generate sample data for testing
    generate_sample_billing_data(1000, 'sample_billing_data.csv')
    
    print("\nSample data generated successfully!")
    print("You can now test the billing audit tool with this data.")
    print("\nTo use with your actual data:")
    print("1. Replace 'sample_billing_data.csv' with your actual file in config.json")
    print("2. Update column mappings in config.json to match your column names")
    print("3. Ensure your tariff_lookup.csv file is in the correct format")
    print("4. Run the main analysis script")